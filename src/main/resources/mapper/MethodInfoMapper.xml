<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whitebye.angel.ai.mapper.MethodInfoMapper">

    <resultMap id="BaseResultMap" type="com.whitebye.angel.ai.common.entity.MethodInfoDO">
        <id column="id" property="id"/>
        <result column="app_name" property="appName"/>
        <result column="package_name" property="packageName"/>
        <result column="class_name" property="className"/>
        <result column="method_name" property="methodName"/>
        <result column="related_code" property="relatedCode"/>
        <result column="code_length" property="codeLength"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, app_name, package_name, class_name, method_name, related_code, code_length, create_time, update_time, deleted
    </sql>

    <insert id="insert" parameterType="com.whitebye.angel.ai.common.entity.MethodInfoDO">
        INSERT INTO method_info (
            app_name, package_name, class_name, method_name, related_code, code_length,
            create_time, update_time, deleted
        ) VALUES (
            #{appName}, #{packageName}, #{className}, #{methodName}, #{relatedCode}, #{codeLength},
            NOW(), NOW(), 0
        )
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO method_info (
            app_name, package_name, class_name, method_name, related_code, code_length,
            create_time, update_time, deleted
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.appName}, #{item.packageName}, #{item.className}, #{item.methodName}, #{item.relatedCode}, #{item.codeLength},
                NOW(), NOW(), 0
            )
        </foreach>
    </insert>

    <update id="fakeDelete">
        UPDATE method_info
        SET deleted = 1, update_time = NOW()
        WHERE id = #{id}
    </update>

    <update id="batchFakeDelete">
        UPDATE method_info
        SET deleted = 1, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateById" parameterType="com.whitebye.angel.ai.common.entity.MethodInfoDO">
        UPDATE method_info
        <set>
            <if test="appName != null">app_name = #{appName},</if>
            <if test="packageName != null">package_name = #{packageName},</if>
            <if test="className != null">class_name = #{className},</if>
            <if test="methodName != null">method_name = #{methodName},</if>
            <if test="relatedCode != null">related_code = #{relatedCode},</if>
            <if test="codeLength != null">code_length = #{codeLength},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <update id="batchUpdateByIds" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE method_info
            <set>
                <if test="item.appName != null">app_name = #{item.appName},</if>
                <if test="item.packageName != null">package_name = #{item.packageName},</if>
                <if test="item.className != null">class_name = #{item.className},</if>
                <if test="item.methodName != null">method_name = #{item.methodName},</if>
                <if test="item.relatedCode != null">related_code = #{item.relatedCode},</if>
                <if test="item.codeLength != null">code_length = #{item.codeLength},</if>
                update_time = NOW()
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>

    <select id="selectByParam" resultMap="BaseResultMap" parameterType="com.whitebye.angel.ai.common.query.MethodInfoQuery">
        SELECT
        <include refid="Base_Column_List"/>
        FROM method_info
        WHERE deleted = 0
        <if test="appName != null and appName != ''">
            AND app_name = #{appName}
        </if>
        <if test="packageName != null and packageName != ''">
            AND package_name = #{packageName}
        </if>
        <if test="className != null and className != ''">
            AND class_name = #{className}
        </if>
        <if test="methodName != null and methodName != ''">
            AND method_name = #{methodName}
        </if>
        ORDER BY create_time DESC
    </select>

</mapper>
