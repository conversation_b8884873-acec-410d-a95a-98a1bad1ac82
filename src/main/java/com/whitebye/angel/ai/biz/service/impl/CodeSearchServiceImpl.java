package com.whitebye.angel.ai.biz.service.impl;

import com.github.javaparser.JavaParser;
import com.github.javaparser.ParseResult;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.body.ClassOrInterfaceDeclaration;
import com.github.javaparser.ast.body.EnumDeclaration;
import com.github.javaparser.ast.body.MethodDeclaration;
import com.github.javaparser.ast.body.TypeDeclaration;
import com.github.javaparser.ast.body.AnnotationDeclaration;
import com.whitebye.angel.ai.biz.service.CodeSearchService;
import com.whitebye.angel.ai.common.entity.AppInfoDO;
import com.whitebye.angel.ai.common.entity.ClassRelatedInfoDO;
import com.whitebye.angel.ai.common.entity.MethodInfoDO;
import com.whitebye.angel.ai.common.query.AppInfoQuery;
import com.whitebye.angel.ai.mapper.AppInfoMapper;
import com.whitebye.angel.ai.mapper.ClassRelatedInfoMapper;
import com.whitebye.angel.ai.mapper.MethodInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.Arrays;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CodeSearchServiceImpl implements CodeSearchService {

    @Resource
    private AppInfoMapper appInfoMapper;

    @Resource
    private MethodInfoMapper methodInfoMapper;

    @Resource
    private ClassRelatedInfoMapper classRelatedInfoMapper;

    @Value("${git.username}")
    private String gitUsername;

    @Value("${git.password}")
    private String gitPassword;

    @Override
    public Boolean analyzeSearchCode(List<String> appNameList) {
        if (CollectionUtils.isEmpty(appNameList)) {
            return Boolean.FALSE;
        }

        for (String appName : appNameList) {
            log.info("开始处理应用: {}", appName);
            
            // 1. 根据appName查询app_info获取代码仓库地址
            AppInfoQuery query = new AppInfoQuery();
            query.setAppName(appName);
            List<AppInfoDO> appInfoList = appInfoMapper.selectByParam(query);
            if (CollectionUtils.isEmpty(appInfoList)) {
                log.warn("未找到应用信息: {}", appName);
                continue;
            }
            
            AppInfoDO appInfo = appInfoList.get(0);
            String codeRepositoryUrl = appInfo.getCodeRepositoryUrl();
            Long appId = appInfo.getId();
            
            // 2. 使用GIT工具拉取应用代码
            String localPath = System.getProperty("java.io.tmpdir") + "/angel-ai-search/" + appName;
            File localDir = new File(localPath);
            if (localDir.exists()) {
                deleteDir(localDir);
            }
            
            try {
                Git.cloneRepository()
                        .setURI(codeRepositoryUrl)
                        .setDirectory(localDir)
                        .setBranch("master")
                        .setCredentialsProvider(new UsernamePasswordCredentialsProvider(gitUsername, gitPassword))
                        .call();
            } catch (Exception e) {
                log.error("拉取代码仓库失败: {}", codeRepositoryUrl, e);
                continue;
            }
            
            // 3. 处理所有JAVA代码
            List<File> srcDirs = new ArrayList<>();
            findSrcMainJavaDirs(localDir, srcDirs);
            
            for (File srcDir : srcDirs) {
                List<File> javaFiles = new ArrayList<>();
                collectJavaFiles(srcDir, javaFiles);
                
                for (File javaFile : javaFiles) {
                    processJavaFile(javaFile, appId, appName);
                }
            }
            
            // 清理临时目录
            deleteDir(localDir);
        }
        
        return Boolean.TRUE;
    }

    @Override
    public Boolean initAnalyzeSearch(List<String> appNameList) {
        return null;
    }

    private void processJavaFile(File javaFile, Long appId, String appName) {
        try (FileInputStream fis = new FileInputStream(javaFile)) {
            JavaParser javaParser = new JavaParser();
            ParseResult<CompilationUnit> result = javaParser.parse(fis, StandardCharsets.UTF_8);
            
            if (!result.isSuccessful() || !result.getResult().isPresent()) {
                log.error("JavaParser解析失败: {}", javaFile.getAbsolutePath());
                return;
            }
            
            CompilationUnit cu = result.getResult().get();
            String packageName = cu.getPackageDeclaration().map(pd -> pd.getName().asString()).orElse("");
            
            // 获取package和import信息
            StringBuilder packageAndImports = new StringBuilder();
            if (cu.getPackageDeclaration().isPresent()) {
                packageAndImports.append(cu.getPackageDeclaration().get().toString()).append("\n");
            }
            cu.getImports().forEach(importDecl -> 
                packageAndImports.append(importDecl.toString()).append("\n")
            );
            
            // 处理所有类型声明
            for (TypeDeclaration<?> typeDecl : cu.getTypes()) {
                processTypeDeclaration(typeDecl, packageName, appId, appName, "", packageAndImports.toString());
            }
            
        } catch (Exception e) {
            log.error("处理Java文件失败: {}", javaFile.getAbsolutePath(), e);
        }
    }

    private void processTypeDeclaration(TypeDeclaration<?> typeDecl, String packageName, Long appId, String appName, String parentClassName, String packageAndImports) {
        String className = parentClassName.isEmpty() ? typeDecl.getNameAsString() : parentClassName + "." + typeDecl.getNameAsString();
        
        // 处理方法
        List<MethodDeclaration> methods = typeDecl.getMethods();
        for (MethodDeclaration method : methods) {
            saveMethodInfo(method, packageName, className, appId, appName);
        }
        
        // 获取除方法外的所有代码作为class_related_info（不包含内部类）
        String classRelatedCode = getClassRelatedCode(typeDecl);
        String fullClassCode = packageAndImports + "\n" + classRelatedCode;
        saveClassRelatedInfo(fullClassCode, packageName, className, appId, appName, typeDecl);
        
        // 处理内部类型（作为独立的类信息记录）
        AtomicInteger anonymousCounter = new AtomicInteger(1);
        typeDecl.getChildNodes().forEach(child -> {
            if (child instanceof ClassOrInterfaceDeclaration) {
                ClassOrInterfaceDeclaration innerClass = (ClassOrInterfaceDeclaration) child;
                String innerClassName;
                if (innerClass.getNameAsString().isEmpty()) {
                    // 匿名内部类：父类名.$序号
                    innerClassName = className + ".$" + anonymousCounter.getAndIncrement();
                } else {
                    // 普通内部类：父类名.内部类名
                    innerClassName = className + "." + innerClass.getNameAsString();
                }
                processTypeDeclaration(innerClass, packageName, appId, appName, "", packageAndImports);
            } else if (child instanceof EnumDeclaration) {
                EnumDeclaration innerEnum = (EnumDeclaration) child;
                // 内部枚举：父类名.枚举名
                processTypeDeclaration(innerEnum, packageName, appId, appName, "", packageAndImports);
            } else if (child instanceof AnnotationDeclaration) {
                AnnotationDeclaration innerAnnotation = (AnnotationDeclaration) child;
                // 内部注解：父类名.注解名
                processTypeDeclaration(innerAnnotation, packageName, appId, appName, "", packageAndImports);
            }
        });
    }

    private String getClassRelatedCode(TypeDeclaration<?> typeDecl) {
        StringBuilder classRelatedCode = new StringBuilder();
        
        // 1. 添加类声明行（包含注解、修饰符、类名、继承等）
        String classDeclaration = getClassDeclarationLine(typeDecl);
        classRelatedCode.append(classDeclaration).append("\n");
        
        // 2. 添加字段声明（包括静态变量和实例变量）
        typeDecl.getFields().forEach(field -> {
            String fieldCode = removeComments(field.toString());
            classRelatedCode.append(fieldCode).append("\n");
        });
        
        // 3. 添加构造函数
        typeDecl.getConstructors().forEach(constructor -> {
            String constructorCode = removeComments(constructor.toString());
            classRelatedCode.append(constructorCode).append("\n");
        });
        
        // 4. 添加静态代码块和实例初始化块
        typeDecl.getChildNodes().forEach(child -> {
            if (child instanceof com.github.javaparser.ast.stmt.BlockStmt) {
                String blockCode = removeComments(child.toString());
                classRelatedCode.append(blockCode).append("\n");
            }
        });
        
        // 5. 添加类结束大括号
        classRelatedCode.append("}");
        
        return classRelatedCode.toString();
    }

    private String getClassDeclarationLine(TypeDeclaration<?> typeDecl) {
        StringBuilder declaration = new StringBuilder();
        
        // 添加注解
        typeDecl.getAnnotations().forEach(annotation -> {
            declaration.append(annotation.toString()).append("\n");
        });
        
        // 添加修饰符
        if (typeDecl.getModifiers().isNonEmpty()) {
            typeDecl.getModifiers().forEach(modifier -> {
                declaration.append(modifier.getKeyword().asString()).append(" ");
            });
        }
        
        // 添加类型关键字和名称
        if (typeDecl instanceof ClassOrInterfaceDeclaration) {
            ClassOrInterfaceDeclaration classOrInterface = (ClassOrInterfaceDeclaration) typeDecl;
            declaration.append(classOrInterface.isInterface() ? "interface " : "class ");
            declaration.append(typeDecl.getNameAsString());
            
            // 添加泛型参数
            if (classOrInterface.getTypeParameters().isNonEmpty()) {
                declaration.append("<");
                classOrInterface.getTypeParameters().forEach(param -> {
                    declaration.append(param.toString()).append(", ");
                });
                declaration.setLength(declaration.length() - 2); // 移除最后的", "
                declaration.append(">");
            }
            
            // 添加继承和实现
            if (classOrInterface.getExtendedTypes().isNonEmpty()) {
                declaration.append(" extends ");
                classOrInterface.getExtendedTypes().forEach(type -> {
                    declaration.append(type.toString()).append(", ");
                });
                declaration.setLength(declaration.length() - 2);
            }
            
            if (classOrInterface.getImplementedTypes().isNonEmpty()) {
                declaration.append(" implements ");
                classOrInterface.getImplementedTypes().forEach(type -> {
                    declaration.append(type.toString()).append(", ");
                });
                declaration.setLength(declaration.length() - 2);
            }
        } else if (typeDecl instanceof EnumDeclaration) {
            declaration.append("enum ").append(typeDecl.getNameAsString());
            EnumDeclaration enumDecl = (EnumDeclaration) typeDecl;
            if (enumDecl.getImplementedTypes().isNonEmpty()) {
                declaration.append(" implements ");
                enumDecl.getImplementedTypes().forEach(type -> {
                    declaration.append(type.toString()).append(", ");
                });
                declaration.setLength(declaration.length() - 2);
            }
        } else if (typeDecl instanceof AnnotationDeclaration) {
            declaration.append("@interface ").append(typeDecl.getNameAsString());
        }
        
        declaration.append(" {");
        
        return declaration.toString();
    }

    private String removeComments(String code) {
        StringBuilder result = new StringBuilder();
        boolean inSingleLineComment = false;
        boolean inMultiLineComment = false;
        boolean inString = false;
        char prev = 0;
        
        for (int i = 0; i < code.length(); i++) {
            char current = code.charAt(i);
            char next = (i + 1 < code.length()) ? code.charAt(i + 1) : 0;
            
            // 处理字符串内容，字符串内的注释符号不处理
            if (!inSingleLineComment && !inMultiLineComment) {
                if (current == '"' && prev != '\\') {
                    inString = !inString;
                    result.append(current);
                    prev = current;
                    continue;
                }
            }
            
            if (inString) {
                result.append(current);
                prev = current;
                continue;
            }
            
            // 处理单行注释 //
            if (!inMultiLineComment && current == '/' && next == '/') {
                inSingleLineComment = true;
                i++; // 跳过下一个字符
                prev = current;
                continue;
            }
            
            // 处理多行注释开始 /*
            if (!inSingleLineComment && current == '/' && next == '*') {
                inMultiLineComment = true;
                i++; // 跳过下一个字符
                prev = current;
                continue;
            }
            
            // 处理多行注释结束 */
            if (inMultiLineComment && current == '*' && next == '/') {
                inMultiLineComment = false;
                i++; // 跳过下一个字符
                prev = current;
                continue;
            }
            
            // 处理单行注释结束（换行）
            if (inSingleLineComment && (current == '\n' || current == '\r')) {
                inSingleLineComment = false;
                result.append(current);
                prev = current;
                continue;
            }
            
            // 如果不在注释中，添加字符
            if (!inSingleLineComment && !inMultiLineComment) {
                result.append(current);
            }
            
            prev = current;
        }
        
        return result.toString();
    }

    private boolean isConstructor(String line, String className) {
        String trimmed = line.trim();
        // 检查是否为构造函数
        return trimmed.matches(".*\\b" + className + "\\s*\\([^)]*\\).*");
    }

    private int countBraces(String line, char brace) {
        int count = 0;
        boolean inString = false;
        char prev = 0;
        
        for (char c : line.toCharArray()) {
            if (c == '"' && prev != '\\') {
                inString = !inString;
            }
            if (!inString && c == brace) {
                count++;
            }
            prev = c;
        }
        return count;
    }

    private void saveMethodInfo(MethodDeclaration method, String packageName, String className, Long appId, String appName) {
        MethodInfoDO methodInfo = new MethodInfoDO();
        methodInfo.setAppName(appName);
        methodInfo.setPackageName(packageName);
        methodInfo.setClassName(className);
        methodInfo.setMethodName(method.getNameAsString());
        
        // 删除方法代码中的注释和空行
        String methodCode = method.toString();
        String codeWithoutComments = removeComments(methodCode);
        String codeWithoutEmptyLines = removeEmptyLines(codeWithoutComments);
        
        methodInfo.setRelatedCode(codeWithoutEmptyLines);
        methodInfo.setCodeLength(codeWithoutEmptyLines.length());
        
        try {
            methodInfoMapper.insert(methodInfo);
            log.debug("保存方法信息成功: {}.{}.{}", packageName, className, method.getNameAsString());
        } catch (Exception e) {
            log.error("保存方法信息失败: {}.{}.{}", packageName, className, method.getNameAsString(), e);
        }
    }

    private void saveClassRelatedInfo(String classRelatedCode, String packageName, String className, Long appId, String appName, TypeDeclaration<?> typeDecl) {
        ClassRelatedInfoDO classRelatedInfo = new ClassRelatedInfoDO();
        classRelatedInfo.setAppName(appName);
        classRelatedInfo.setPackageName(packageName);
        classRelatedInfo.setClassName(className);
        
        // 删除空行
        String codeWithoutEmptyLines = removeEmptyLines(classRelatedCode);
        
        classRelatedInfo.setRelatedCode(codeWithoutEmptyLines);
        classRelatedInfo.setCodeLength(codeWithoutEmptyLines.length());
        
        // 根据实际类型设置classType：1=类，2=接口，3=枚举，4=注解
        if (typeDecl instanceof ClassOrInterfaceDeclaration) {
            ClassOrInterfaceDeclaration classOrInterface = (ClassOrInterfaceDeclaration) typeDecl;
            classRelatedInfo.setClassType(classOrInterface.isInterface() ? 2 : 1);
        } else if (typeDecl instanceof EnumDeclaration) {
            classRelatedInfo.setClassType(3);
        } else if (typeDecl instanceof AnnotationDeclaration) {
            classRelatedInfo.setClassType(4);
        } else {
            classRelatedInfo.setClassType(1); // 默认为类
        }
        
        // 处理父类信息
        if (className.contains(".")) {
            String[] parts = className.split("\\.");
            if (parts.length > 1) {
                classRelatedInfo.setParentClassName(parts[0]);
                classRelatedInfo.setParentPackageName(packageName);
            }
        }
        
        try {
            classRelatedInfoMapper.insert(classRelatedInfo);
            log.debug("保存类相关信息成功: {}.{}", packageName, className);
        } catch (Exception e) {
            log.error("保存类相关信息失败: {}.{}", packageName, className, e);
        }
    }

    private String removeEmptyLines(String code) {
        return Arrays.stream(code.split("\n"))
                .filter(line -> !line.trim().isEmpty())
                .collect(Collectors.joining("\n"));
    }

    private boolean isMethodAnnotation(String line) {
        String trimmed = line.trim();
        // 常见的方法注解
        return trimmed.startsWith("@GetMapping") || 
               trimmed.startsWith("@PostMapping") || 
               trimmed.startsWith("@PutMapping") || 
               trimmed.startsWith("@DeleteMapping") || 
               trimmed.startsWith("@RequestMapping") || 
               trimmed.startsWith("@Override") ||
               trimmed.startsWith("@Transactional") ||
               trimmed.startsWith("@Async") ||
               trimmed.startsWith("@Cacheable") ||
               trimmed.startsWith("@CacheEvict") ||
               trimmed.startsWith("@Scheduled") ||
               trimmed.startsWith("@EventListener") ||
               trimmed.startsWith("@Valid") ||
               trimmed.startsWith("@Validated") ||
               // 通用注解模式：@注解名(可能有参数)
               (trimmed.matches("@\\w+\\s*\\(.*\\)\\s*$") || trimmed.matches("@\\w+\\s*$"));
    }

    private boolean isMethodDeclaration(String line, String className) {
        String trimmed = line.trim();
        
        // 排除类声明、接口声明、枚举声明
        if (trimmed.startsWith("class ") || trimmed.startsWith("interface ") || 
            trimmed.startsWith("enum ") || trimmed.startsWith("@interface ")) {
            return false;
        }
        
        // 排除字段声明（通常以分号结尾或者是赋值语句且不包含括号）
        if (trimmed.endsWith(";") || (trimmed.contains("=") && !trimmed.contains("("))) {
            return false;
        }
        
        // 排除注解声明
        if (trimmed.startsWith("@")) {
            return false;
        }
        
        // 检查是否包含方法特征：方法名 + 参数列表
        boolean hasParentheses = trimmed.contains("(") && trimmed.contains(")");
        if (!hasParentheses) {
            return false;
        }
        
        // 检查是否是构造函数或普通方法
        boolean isConstructor = trimmed.contains(className + "(");
        boolean isMethod = trimmed.matches(".*\\b(public|private|protected|static|final|abstract|synchronized)\\s+.*\\w+\\s*\\([^)]*\\).*") ||
                          trimmed.matches(".*\\w+\\s+\\w+\\s*\\([^)]*\\).*"); // 返回类型 + 方法名 + 参数
        
        return isConstructor || isMethod;
    }

    private boolean isClassDeclaration(String line) {
        String trimmed = line.trim();
        return trimmed.startsWith("public class ") || 
               trimmed.startsWith("private class ") || 
               trimmed.startsWith("protected class ") ||
               trimmed.startsWith("class ") ||
               trimmed.startsWith("public interface ") || 
               trimmed.startsWith("private interface ") || 
               trimmed.startsWith("protected interface ") ||
               trimmed.startsWith("interface ") ||
               trimmed.startsWith("public enum ") || 
               trimmed.startsWith("private enum ") || 
               trimmed.startsWith("protected enum ") ||
               trimmed.startsWith("enum ") ||
               trimmed.startsWith("public @interface ") || 
               trimmed.startsWith("private @interface ") || 
               trimmed.startsWith("protected @interface ") ||
               trimmed.startsWith("@interface ");
    }

    // 复用CodeServiceImpl中的工具方法
    private void findSrcMainJavaDirs(File dir, List<File> result) {
        if (dir == null || !dir.exists()) return;
        if (dir.isDirectory() && dir.getName().equals("java") && dir.getParentFile() != null
            && dir.getParentFile().getName().equals("main")
            && dir.getParentFile().getParentFile() != null
            && dir.getParentFile().getParentFile().getName().equals("src")) {
            result.add(dir);
            return;
        }
        File[] files = dir.listFiles();
        if (files == null) return;
        for (File file : files) {
            findSrcMainJavaDirs(file, result);
        }
    }

    private void collectJavaFiles(File dir, List<File> javaFiles) {
        if (dir == null || !dir.exists()) return;
        File[] files = dir.listFiles();
        if (files == null) return;
        for (File file : files) {
            if (file.isDirectory()) {
                collectJavaFiles(file, javaFiles);
            } else if (file.getName().endsWith(".java")) {
                javaFiles.add(file);
            }
        }
    }

    private boolean deleteDir(File dir) {
        if (dir.isDirectory()) {
            File[] children = dir.listFiles();
            if (children != null) {
                for (File child : children) {
                    deleteDir(child);
                }
            }
        }
        return dir.delete();
    }
}
