package com.whitebye.angel.ai.controller;

import com.whitebye.angel.ai.biz.service.CodeSearchService;
import com.whitebye.angel.ai.common.BizResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
public class CodeSearchController {

    @Resource
    private CodeSearchService codeSearchService;

    @PostMapping(value = "/code/search/analyze")
    public BizResult<Boolean> analyzeSearchCode(@RequestBody List<String> appNameList) {
        return BizResult.success(codeSearchService.analyzeSearchCode(appNameList));
    }

    @PostMapping(value = "/init/code/search")
    public BizResult<Boolean> initAnalyzeSearch(@RequestBody List<String> appNameList) {
        return BizResult.success(codeSearchService.initAnalyzeSearch(appNameList));
    }

}
