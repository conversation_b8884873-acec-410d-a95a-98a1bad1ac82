package com.whitebye.angel.ai.biz.service.impl;

import com.whitebye.angel.ai.common.entity.ClassRelatedInfoDO;
import com.whitebye.angel.ai.common.entity.CodeSearchDO;
import com.whitebye.angel.ai.common.entity.MethodInfoDO;
import com.whitebye.angel.ai.common.query.ClassRelatedInfoQuery;
import com.whitebye.angel.ai.common.query.MethodInfoQuery;
import com.whitebye.angel.ai.mapper.ClassRelatedInfoMapper;
import com.whitebye.angel.ai.mapper.CodeSearchMapper;
import com.whitebye.angel.ai.mapper.MethodInfoMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CodeSearchServiceImplTest {

    @Mock
    private MethodInfoMapper methodInfoMapper;

    @Mock
    private ClassRelatedInfoMapper classRelatedInfoMapper;

    @Mock
    private CodeSearchMapper codeSearchMapper;

    @InjectMocks
    private CodeSearchServiceImpl codeSearchService;

    private List<MethodInfoDO> mockMethodInfoList;
    private List<ClassRelatedInfoDO> mockClassRelatedInfoList;

    @BeforeEach
    void setUp() {
        // 准备测试数据 - 方法信息
        mockMethodInfoList = new ArrayList<>();
        MethodInfoDO methodInfo1 = new MethodInfoDO();
        methodInfo1.setAppName("test-app");
        methodInfo1.setPackageName("com.test.service");
        methodInfo1.setClassName("UserService");
        methodInfo1.setMethodName("getUserById");
        methodInfo1.setRelatedCode("public User getUserById(Long id) { return userRepository.findById(id); }");
        mockMethodInfoList.add(methodInfo1);

        MethodInfoDO methodInfo2 = new MethodInfoDO();
        methodInfo2.setAppName("test-app");
        methodInfo2.setPackageName("com.test.controller");
        methodInfo2.setClassName("UserController");
        methodInfo2.setMethodName("getUser");
        methodInfo2.setRelatedCode("@GetMapping(\"/user/{id}\") public ResponseEntity<User> getUser(@PathVariable Long id) { return ResponseEntity.ok(userService.getUserById(id)); }");
        mockMethodInfoList.add(methodInfo2);

        // 准备测试数据 - 类相关信息
        mockClassRelatedInfoList = new ArrayList<>();
        ClassRelatedInfoDO classInfo1 = new ClassRelatedInfoDO();
        classInfo1.setAppName("test-app");
        classInfo1.setPackageName("com.test.service");
        classInfo1.setClassName("UserService");
        classInfo1.setRelatedCode("@Service public class UserService { @Autowired private UserRepository userRepository; }");
        mockClassRelatedInfoList.add(classInfo1);

        ClassRelatedInfoDO classInfo2 = new ClassRelatedInfoDO();
        classInfo2.setAppName("test-app");
        classInfo2.setPackageName("com.test.controller");
        classInfo2.setClassName("UserController");
        classInfo2.setRelatedCode("@RestController @RequestMapping(\"/api\") public class UserController { @Autowired private UserService userService; }");
        mockClassRelatedInfoList.add(classInfo2);
    }

    @Test
    void testInitAnalyzeSearch_Success() {
        // 准备测试数据
        List<String> appNameList = Arrays.asList("test-app");

        // 模拟mapper调用
        when(methodInfoMapper.selectByParam(any(MethodInfoQuery.class))).thenReturn(mockMethodInfoList);
        when(classRelatedInfoMapper.selectByParam(any(ClassRelatedInfoQuery.class))).thenReturn(mockClassRelatedInfoList);
        when(codeSearchMapper.batchInsert(anyList())).thenReturn(4); // 2个方法 + 2个类 = 4条记录

        // 执行测试
        Boolean result = codeSearchService.initAnalyzeSearch(appNameList);

        // 验证结果
        assertTrue(result);

        // 验证mapper调用
        verify(methodInfoMapper, times(1)).selectByParam(any(MethodInfoQuery.class));
        verify(classRelatedInfoMapper, times(1)).selectByParam(any(ClassRelatedInfoQuery.class));
        verify(codeSearchMapper, times(1)).batchInsert(anyList());

        // 验证batchInsert的参数
        verify(codeSearchMapper).batchInsert(argThat(list -> {
            List<CodeSearchDO> codeSearchList = (List<CodeSearchDO>) list;
            return codeSearchList.size() == 4 && // 2个方法 + 2个类
                   codeSearchList.stream().anyMatch(cs -> "getUserById".equals(cs.getMethodName())) &&
                   codeSearchList.stream().anyMatch(cs -> "getUser".equals(cs.getMethodName())) &&
                   codeSearchList.stream().anyMatch(cs -> cs.getMethodName() == null && "UserService".equals(cs.getClassName())) &&
                   codeSearchList.stream().anyMatch(cs -> cs.getMethodName() == null && "UserController".equals(cs.getClassName()));
        }));
    }

    @Test
    void testInitAnalyzeSearch_EmptyAppNameList() {
        // 测试空的应用名列表
        Boolean result = codeSearchService.initAnalyzeSearch(new ArrayList<>());
        
        assertFalse(result);
        
        // 验证没有调用mapper
        verify(methodInfoMapper, never()).selectByParam(any());
        verify(classRelatedInfoMapper, never()).selectByParam(any());
        verify(codeSearchMapper, never()).batchInsert(any());
    }

    @Test
    void testInitAnalyzeSearch_NullAppNameList() {
        // 测试null的应用名列表
        Boolean result = codeSearchService.initAnalyzeSearch(null);
        
        assertFalse(result);
        
        // 验证没有调用mapper
        verify(methodInfoMapper, never()).selectByParam(any());
        verify(classRelatedInfoMapper, never()).selectByParam(any());
        verify(codeSearchMapper, never()).batchInsert(any());
    }

    @Test
    void testInitAnalyzeSearch_NoDataFound() {
        // 准备测试数据
        List<String> appNameList = Arrays.asList("non-existent-app");

        // 模拟mapper返回空列表
        when(methodInfoMapper.selectByParam(any(MethodInfoQuery.class))).thenReturn(new ArrayList<>());
        when(classRelatedInfoMapper.selectByParam(any(ClassRelatedInfoQuery.class))).thenReturn(new ArrayList<>());

        // 执行测试
        Boolean result = codeSearchService.initAnalyzeSearch(appNameList);

        // 验证结果
        assertTrue(result); // 即使没有数据也应该返回true

        // 验证mapper调用
        verify(methodInfoMapper, times(1)).selectByParam(any(MethodInfoQuery.class));
        verify(classRelatedInfoMapper, times(1)).selectByParam(any(ClassRelatedInfoQuery.class));
        verify(codeSearchMapper, never()).batchInsert(any()); // 没有数据时不应该调用batchInsert
    }

    @Test
    void testInitAnalyzeSearch_ExceptionHandling() {
        // 准备测试数据
        List<String> appNameList = Arrays.asList("test-app");

        // 模拟mapper抛出异常
        when(methodInfoMapper.selectByParam(any(MethodInfoQuery.class))).thenThrow(new RuntimeException("Database error"));

        // 执行测试
        Boolean result = codeSearchService.initAnalyzeSearch(appNameList);

        // 验证结果
        assertFalse(result); // 异常时应该返回false

        // 验证mapper调用
        verify(methodInfoMapper, times(1)).selectByParam(any(MethodInfoQuery.class));
        verify(classRelatedInfoMapper, never()).selectByParam(any()); // 异常后不应该继续执行
        verify(codeSearchMapper, never()).batchInsert(any());
    }
}
